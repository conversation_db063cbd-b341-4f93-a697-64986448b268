from paddleocr import PaddleOCR, draw_ocr

# 初始化（自动下载模型，首次运行需联网）
ocr = PaddleOCR(use_angle_cls=True, lang='ch')  # 可指定 use_gpu=False 强制 CPU

# 预测
result = ocr.ocr("test.jpg", cls=True)

# 输出结果（格式：[[bbox], 文本, 置信度]）
for line in result[0]:
    print(line[1][0])  # 提取文本

# 可视化结果
from PIL import Image
image = Image.open("test.jpg").convert('RGB')
boxes = [line[0] for line in result[0]]
txts = [line[1][0] for line in result[0]]
scores = [line[1][1] for line in result[0]]
im_show = draw_ocr(image, boxes, txts, scores)
im_show = Image.fromarray(im_show)
im_show.save('result.jpg')
