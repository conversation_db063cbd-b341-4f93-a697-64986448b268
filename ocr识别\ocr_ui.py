import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from tkinterdnd2 import DND_FILES, TkinterDnD
import threading
import os
from PIL import Image, ImageTk
import pyperclip
from paddleocr import PaddleOCR


class OCRProcessor:
    """OCR处理器类"""
    
    def __init__(self):
        self.ocr = None
        self.initialize_ocr()
    
    def initialize_ocr(self):
        """初始化OCR引擎"""
        try:
            # 启用GPU加速和角度分类
            self.ocr = PaddleOCR(use_angle_cls=True, lang='ch', use_gpu=True)
        except Exception as e:
            print(f"GPU初始化失败，切换到CPU模式: {e}")
            try:
                self.ocr = PaddleOCR(use_angle_cls=True, lang='ch', use_gpu=False)
            except Exception as e:
                raise Exception(f"OCR初始化失败: {e}")
    
    def process_image(self, image_path):
        """处理图片并返回识别结果"""
        try:
            result = self.ocr.ocr(image_path, cls=True)
            if result and result[0]:
                # 提取文本内容
                texts = []
                for line in result[0]:
                    if len(line) >= 2 and len(line[1]) >= 1:
                        texts.append(line[1][0])
                return '\n'.join(texts)
            else:
                return "未识别到文字内容"
        except Exception as e:
            raise Exception(f"图片处理失败: {e}")


class OCRApp:
    """OCR UI应用程序"""

    def __init__(self):
        self.root = TkinterDnD.Tk()
        self.root.title("OCR文字识别工具")
        self.root.geometry("900x600")
        self.root.minsize(800, 500)

        # 窗口居中显示
        self.center_window()

        # 初始化OCR处理器
        self.ocr_processor = OCRProcessor()

        # 当前图片路径和处理状态
        self.current_image_path = None
        self.is_processing = False

        # 创建UI
        self.create_widgets()
        self.setup_drag_drop()

    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()  # 确保窗口尺寸已计算

        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 获取窗口尺寸
        window_width = 900
        window_height = 600

        # 计算居中位置
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # 设置窗口位置
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
    def create_widgets(self):
        """创建UI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧框架（图片区域）
        left_frame = ttk.LabelFrame(main_frame, text="图片区域", padding=10)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 拖拽提示区域
        self.drop_area = tk.Label(
            left_frame,
            text="拖拽图片到此处\n或点击选择图片",
            bg="lightgray",
            fg="gray",
            font=("Arial", 12),
            relief=tk.RAISED,
            bd=2
        )
        self.drop_area.pack(fill=tk.BOTH, expand=True)
        self.drop_area.bind("<Button-1>", self.select_image)
        
        # 右侧框架（结果区域）
        right_frame = ttk.LabelFrame(main_frame, text="识别结果", padding=10)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 文本显示区域
        self.text_area = scrolledtext.ScrolledText(
            right_frame,
            wrap=tk.WORD,
            font=("Arial", 11),
            height=20
        )
        self.text_area.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 按钮框架
        button_frame = ttk.Frame(right_frame)
        button_frame.pack(fill=tk.X)

        # 复制按钮
        self.copy_button = ttk.Button(
            button_frame,
            text="复制文本",
            command=self.copy_text,
            state=tk.DISABLED
        )
        self.copy_button.pack(side=tk.LEFT, padx=(0, 5))

        # 清空按钮
        self.clear_button = ttk.Button(
            button_frame,
            text="清空",
            command=self.clear_results
        )
        self.clear_button.pack(side=tk.LEFT)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.root,
            variable=self.progress_var,
            mode='indeterminate'
        )

        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        self.status_bar = ttk.Label(
            self.root,
            textvariable=self.status_var,
            relief=tk.SUNKEN,
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def setup_drag_drop(self):
        """设置拖拽功能"""
        self.drop_area.drop_target_register(DND_FILES)
        self.drop_area.dnd_bind('<<Drop>>', self.on_drop)
    
    def on_drop(self, event):
        """处理拖拽事件"""
        files = event.data.split()
        if files:
            file_path = files[0].strip('{}')  # 移除可能的大括号
            self.process_dropped_file(file_path)
    
    def select_image(self, event=None):
        """选择图片文件"""
        # 如果正在处理，不允许选择新图片
        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，请稍候...")
            return

        from tkinter import filedialog
        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=[
                ("图片文件", "*.jpg *.jpeg *.png *.bmp *.gif *.tiff"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            self.process_dropped_file(file_path)
    
    def process_dropped_file(self, file_path):
        """处理选择或拖拽的文件"""
        # 如果正在处理，不允许处理新文件
        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，请稍候...")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("错误", "文件不存在")
            return

        # 检查文件类型
        valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff'}
        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext not in valid_extensions:
            messagebox.showerror("错误", "不支持的文件格式")
            return

        self.current_image_path = file_path
        self.display_image(file_path)
        self.start_ocr_processing(file_path)
    
    def display_image(self, image_path):
        """显示图片预览"""
        try:
            # 打开并调整图片大小
            image = Image.open(image_path)
            
            # 计算缩放比例
            display_size = (400, 300)
            image.thumbnail(display_size, Image.Resampling.LANCZOS)
            
            # 转换为tkinter可用的格式
            photo = ImageTk.PhotoImage(image)
            
            # 更新显示区域
            self.drop_area.configure(image=photo, text="")
            self.drop_area.image = photo  # 保持引用
            
        except Exception as e:
            messagebox.showerror("错误", f"图片显示失败: {e}")
    
    def start_ocr_processing(self, image_path):
        """开始OCR处理（异步）"""
        # 设置处理状态
        self.is_processing = True
        self.status_var.set("正在识别文字...")
        self.copy_button.configure(state=tk.DISABLED)
        self.clear_button.configure(state=tk.DISABLED)

        # 显示进度条
        self.progress_bar.pack(side=tk.BOTTOM, fill=tk.X, before=self.status_bar)
        self.progress_bar.start(10)  # 开始动画

        # 清空之前的结果
        self.text_area.delete(1.0, tk.END)
        self.text_area.insert(1.0, "正在识别中，请稍候...")

        # 在新线程中处理OCR
        thread = threading.Thread(
            target=self.ocr_worker,
            args=(image_path,),
            daemon=True
        )
        thread.start()
    
    def ocr_worker(self, image_path):
        """OCR工作线程"""
        try:
            result_text = self.ocr_processor.process_image(image_path)
            # 在主线程中更新UI
            self.root.after(0, self.update_results, result_text, None)
        except Exception as e:
            self.root.after(0, self.update_results, None, str(e))
    
    def update_results(self, text, error):
        """更新识别结果"""
        # 停止进度条并隐藏
        self.progress_bar.stop()
        self.progress_bar.pack_forget()

        # 重置处理状态
        self.is_processing = False
        self.clear_button.configure(state=tk.NORMAL)

        if error:
            self.status_var.set(f"识别失败: {error}")
            self.text_area.delete(1.0, tk.END)
            self.text_area.insert(1.0, f"识别失败: {error}")
            messagebox.showerror("错误", f"OCR处理失败: {error}")
        else:
            self.text_area.delete(1.0, tk.END)
            self.text_area.insert(1.0, text)
            self.copy_button.configure(state=tk.NORMAL)
            self.status_var.set("识别完成")
    
    def copy_text(self):
        """复制文本到剪贴板"""
        text = self.text_area.get(1.0, tk.END).strip()
        if text:
            pyperclip.copy(text)
            self.status_var.set("文本已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的文本")
    
    def clear_results(self):
        """清空结果"""
        # 如果正在处理，不允许清空
        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，无法清空")
            return

        self.text_area.delete(1.0, tk.END)
        self.drop_area.configure(
            image="",
            text="拖拽图片到此处\n或点击选择图片"
        )
        self.drop_area.image = None
        self.copy_button.configure(state=tk.DISABLED)
        self.current_image_path = None
        self.status_var.set("就绪")
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


if __name__ == "__main__":
    try:
        app = OCRApp()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        input("按回车键退出...")
